#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon多线程爬虫系统
从zz_amazon_list_tasks获取任务，支持多线程、代理轮换、用户代理轮换
"""

import os
import sys
import time
import random
import threading
import queue
import signal
import pymysql
import requests
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from logging.handlers import RotatingFileHandler
from bs4 import BeautifulSoup
from decimal import Decimal
import re
from urllib.parse import urljoin, urlparse, parse_qs, urlencode, urlunparse

# 导入自定义模块
from user_agents import get_headers, get_random_user_agent
from proxy_manager import FyndiqProxyManager
from dynamic_delay_manager import dynamic_delay_manager, get_smart_delay, record_request_result, get_503_smart_delay

# 配置日志
def setup_logging():
    """设置日志配置"""
    # 创建logs目录
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # 创建logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # 清除现有的handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 文件日志处理器 - 主日志
    file_handler = RotatingFileHandler(
        'logs/amazon_crawler.log',
        maxBytes=50*1024*1024,  # 50MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    
    # 错误日志处理器
    error_handler = RotatingFileHandler(
        'logs/amazon_crawler_error.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=3,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    
    # 控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    error_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(error_handler)
    logger.addHandler(console_handler)
    
    return logger

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'user': 'root',
    'password': 'xh884813@@@XH',
    'database': 'xace200_lsh',
    'port': 22888,
    'charset': 'utf8mb4',
    'autocommit': True
}

# 爬虫配置
CRAWLER_CONFIG = {
    'max_workers': 20,           # 最大线程数
    'request_timeout': 30,      # 请求超时时间
    'retry_count': 3,           # 重试次数
    'delay_range': (2, 5),      # 请求间隔范围（秒）
    'page_delay_range': (3, 8), # 页面间隔范围（秒）
    'use_proxy': False,         # 关闭代理功能
    'rotate_user_agent': True,  # 是否轮换用户代理
}

class AmazonCrawler:
    """Amazon爬虫类"""
    
    def __init__(self, config=None):
        """初始化爬虫"""
        self.config = config or CRAWLER_CONFIG
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 根据配置决定是否初始化代理管理器
        if self.config.get('use_proxy'):
            self.proxy_manager = FyndiqProxyManager(DB_CONFIG)
            self.proxy_manager.load_proxies()
            self.logger.info("代理管理器初始化完成")
        else:
            self.proxy_manager = None
            self.logger.info("🚫 代理功能已关闭，使用直连模式")

        # 线程控制
        self.running = True
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        
        # 统计信息
        self.stats = {
            'tasks_processed': 0,
            'tasks_success': 0,
            'tasks_failed': 0,
            'pages_crawled': 0,
            'products_found': 0,
            'start_time': None,
            'errors': [],
            'proxy_usage': {},  # 代理使用统计
            'proxy_switches': 0  # 代理切换次数
        }

        # 代理轮换管理
        self.task_proxy_usage = {}  # 每个任务的代理使用计数 {task_id: {'proxy': 'ip:port', 'count': 10}}
        self.proxy_rotation_threshold = 10  # 同一任务使用同一代理的最大请求数

        # 线程锁
        self.stats_lock = threading.Lock()
        self.proxy_lock = threading.Lock()
    
    def get_db_connection(self):
        """获取数据库连接"""
        return pymysql.connect(**DB_CONFIG)
    
    def load_pending_tasks(self):
        """从数据库加载待处理任务"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            # 查询待处理的任务
            query = """
                SELECT id, url, source_category_id, max_pages_to_crawl, crawled_pages, status
                FROM zz_amazon_list_tasks
                WHERE status IN ('pending', 'in_progress')
                ORDER BY created_at ASC
                LIMIT 100
            """
            cursor.execute(query)
            tasks = cursor.fetchall()
            
            self.logger.info(f"从数据库加载了 {len(tasks)} 个待处理任务")
            
            # 将任务添加到队列
            for task in tasks:
                self.task_queue.put(task)
            
            return len(tasks)
            
        except Exception as e:
            self.logger.error(f"加载任务失败: {e}")
            return 0
        finally:
            if 'conn' in locals():
                conn.close()
    
    def update_task_status(self, task_id, status, crawled_pages=None, error_message=None):
        """更新任务状态"""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            update_fields = ["status = %s", "updated_at = NOW()"]
            update_values = [status]
            
            if crawled_pages is not None:
                update_fields.append("crawled_pages = %s")
                update_values.append(crawled_pages)
            
            if error_message is not None:
                update_fields.append("error_message = %s")
                update_values.append(error_message)
            
            update_values.append(task_id)
            
            query = f"UPDATE zz_amazon_list_tasks SET {', '.join(update_fields)} WHERE id = %s"
            cursor.execute(query, update_values)
            
            self.logger.debug(f"更新任务 {task_id} 状态: {status}")
            
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {e}")
        finally:
            if 'conn' in locals():
                conn.close()
    
    def get_proxy_for_task(self, task_id, force_new=False):
        """为任务获取代理，支持智能轮换"""
        with self.proxy_lock:
            current_usage = self.task_proxy_usage.get(task_id, {'proxy': None, 'count': 0})

            # 检查是否需要更换代理
            need_new_proxy = (
                force_new or
                current_usage['proxy'] is None or
                current_usage['count'] >= self.proxy_rotation_threshold
            )

            if need_new_proxy and self.proxy_manager:
                # 获取新代理
                proxy_dict = self.proxy_manager.get_random_proxy()
                if proxy_dict:
                    new_proxy_ip = proxy_dict['http'].split('://')[-1].split('@')[-1]

                    # 如果强制更换，记录切换
                    if force_new and current_usage['proxy']:
                        with self.stats_lock:
                            self.stats['proxy_switches'] += 1
                        self.logger.info(f"任务 {task_id} 强制切换代理: {current_usage['proxy']} → {new_proxy_ip}")

                    # 更新任务代理使用记录
                    self.task_proxy_usage[task_id] = {
                        'proxy': new_proxy_ip,
                        'proxy_dict': proxy_dict,
                        'count': 0
                    }

                    return proxy_dict, new_proxy_ip

            elif current_usage['proxy']:
                # 使用当前代理
                return current_usage.get('proxy_dict'), current_usage['proxy']

            return None, None

    def record_proxy_usage(self, task_id, proxy_ip, success=True):
        """记录代理使用情况"""
        with self.proxy_lock:
            if task_id in self.task_proxy_usage:
                self.task_proxy_usage[task_id]['count'] += 1

                # 更新统计
                with self.stats_lock:
                    if proxy_ip not in self.stats['proxy_usage']:
                        self.stats['proxy_usage'][proxy_ip] = {'success': 0, 'failed': 0}

                    if success:
                        self.stats['proxy_usage'][proxy_ip]['success'] += 1
                    else:
                        self.stats['proxy_usage'][proxy_ip]['failed'] += 1

    def make_request(self, url, task_id=None, **kwargs):
        """发送HTTP请求，支持503错误处理和代理轮换 - 已关闭代理"""
        retry_count = kwargs.pop('retry_count', self.config.get('retry_count', 3))
        force_new_proxy = False  # 控制是否强制更换代理

        for attempt in range(retry_count):
            try:
                # 获取请求头 - 优化美国地址支持
                if self.config.get('rotate_user_agent'):
                    headers = get_headers()
                else:
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Sec-Fetch-User': '?1',
                        'Upgrade-Insecure-Requests': '1',
                        # 确保支持美国地址
                        'Cookie': 'sp-cdn="L5Z9:US"; session-id=147-1234567-1234567'
                    }

                # 合并自定义headers
                if 'headers' in kwargs:
                    headers.update(kwargs['headers'])
                kwargs['headers'] = headers

                # 移除代理相关逻辑，使用直连
                proxy_ip = None
                proxy_dict = None

                # 提取并移除自定义参数，避免传递给requests
                kwargs.pop('force_new_proxy', None)  # 移除代理相关参数

                if not kwargs.get('timeout'):
                    kwargs['timeout'] = self.config.get('request_timeout', 30)

                # 发送请求 - 直连模式
                start_time = time.time()
                
                if task_id:
                    self.logger.info(f"任务 {task_id} 使用直连模式")

                response = requests.get(url, **kwargs)
                response_time = time.time() - start_time

                # 检查响应状态
                if response.status_code == 503:
                    self.logger.warning(f"收到503错误 (尝试 {attempt + 1}/{retry_count}): {url} [直连模式]")

                    # 记录503错误到动态延迟管理器
                    record_request_result(False, '503')

                    if attempt < retry_count - 1:
                        # 使用智能503延迟
                        delay = get_503_smart_delay(attempt + 1)
                        self.logger.info(f"503错误智能延迟 {delay:.1f} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        raise requests.exceptions.HTTPError(f"503 Service Unavailable after {retry_count} attempts")

                elif response.status_code == 429:
                    self.logger.warning(f"收到429限流错误 (尝试 {attempt + 1}/{retry_count}): {url} [直连模式]")

                    if attempt < retry_count - 1:
                        # 429错误延迟
                        delay = random.uniform(60, 120) * (attempt + 1)
                        self.logger.info(f"429错误延迟 {delay:.1f} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        raise requests.exceptions.HTTPError(f"429 Too Many Requests after {retry_count} attempts")

                elif response.status_code >= 400:
                    response.raise_for_status()

                # 记录成功请求到动态延迟管理器
                record_request_result(True)

                self.logger.info(f"请求成功: {url} (耗时: {response_time:.2f}s) [直连模式]")
                return response

            except requests.exceptions.HTTPError as e:
                if "503" in str(e) or "429" in str(e):
                    # HTTP错误已经在上面处理了
                    raise
                else:
                    self.logger.error(f"HTTP错误 (尝试 {attempt + 1}/{retry_count}): {url} - {e}")
                    if attempt < retry_count - 1:
                        delay = random.uniform(5, 10)
                        time.sleep(delay)
                        continue
                    else:
                        raise

            except Exception as e:
                self.logger.error(f"请求失败 (尝试 {attempt + 1}/{retry_count}): {url} - {e} [直连模式]")

                if attempt < retry_count - 1:
                    delay = random.uniform(3, 6)
                    time.sleep(delay)
                    continue
                else:
                    raise
    
    def extract_products_from_page(self, soup):
        """
        从页面提取商品信息 - 优化版本
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            list: 商品信息列表
        """
        products = []
        
        try:
            # 查找商品容器
            containers = soup.select('[data-component-type="s-search-result"]')
            self.logger.info(f"找到 {len(containers)} 个商品容器")
            
            for i, container in enumerate(containers, 1):
                try:
                    product = self.extract_single_product(container)
                    if product:
                        product['list_position'] = i
                        products.append(product)
                        
                except Exception as e:
                    self.logger.error(f"提取第 {i} 个商品失败: {e}")
                    continue
            
            self.logger.info(f"成功提取 {len(products)} 个商品")
            return products
            
        except Exception as e:
            self.logger.error(f"提取商品失败: {e}")
            return []

    def extract_single_product(self, container):
        """
        提取单个商品信息 - 增加Prime信息调试
        
        Args:
            container: 商品容器元素
            
        Returns:
            dict: 商品信息
        """
        try:
            product = {}
            
            # 提取ASIN
            asin = container.get('data-asin', '')
            if not asin:
                return None
            product['asin'] = asin
            
            # 提取标题
            title_selectors = [
                'h2 a span',
                '[data-cy="title-recipe"] h2 span',
                'h2 span',
                '[data-cy="title-recipe-title"]'
            ]
            
            title = ''
            for selector in title_selectors:
                title_elem = container.select_one(selector)
                if title_elem:
                    title = title_elem.get_text(strip=True)
                    if title:
                        break
            
            product['title'] = title[:512] if title else ''
            
            # 提取价格信息
            price_info = self._extract_price_info(container)
            product.update(price_info)
            
            # 提取其他基本信息
            product['image_url'] = self._extract_image_url(container)
            product['product_url'] = self._extract_product_url(container)
            
            # 提取评分和评论数
            rating_info = self._extract_rating_info(container)
            product.update(rating_info)
            
            # 提取Prime信息 - 增加调试日志
            self.logger.debug(f"开始提取ASIN {asin} 的Prime信息")
            prime_info = self._extract_prime_info_enhanced(container)
            product['prime_info'] = prime_info
            
            if prime_info:
                self.logger.info(f"✅ ASIN {asin} Prime信息提取成功: {prime_info[:50]}")
            else:
                self.logger.warning(f"⚠️ ASIN {asin} Prime信息提取失败")
            
            # 检测广告商品
            product['is_sponsored'] = self._detect_sponsored(container)
            
            # 提取购买数量
            product['bought_num'] = self._extract_bought_info(container)
            
            return product
            
        except Exception as e:
            self.logger.error(f"提取商品信息失败: {e}")
            return None

    def _extract_price_info(self, container):
        """提取价格信息"""
        price_info = {
            'price': 0.00,
            'original_price': 0.00,
            'discount_percent': 0
        }
        
        try:
            # 提取当前价格
            price_selectors = [
                '.a-price .a-offscreen',
                '.a-price-whole',
                '.a-price .a-price-whole',
                '.a-price-range .a-price .a-offscreen'
            ]
            
            for selector in price_selectors:
                price_elem = container.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', '').replace('$', ''))
                    if price_match:
                        try:
                            price_info['price'] = float(price_match.group())
                            break
                        except:
                            continue
            
            # 提取原价
            original_price_selectors = [
                '.a-text-price .a-offscreen',
                '[data-a-strike="true"] .a-offscreen',
                '.a-price.a-text-price .a-offscreen'
            ]
            
            for selector in original_price_selectors:
                orig_elem = container.select_one(selector)
                if orig_elem:
                    orig_text = orig_elem.get_text(strip=True)
                    orig_match = re.search(r'[\d,]+\.?\d*', orig_text.replace(',', '').replace('$', ''))
                    if orig_match:
                        try:
                            price_info['original_price'] = float(orig_match.group())
                            break
                        except:
                            continue
            
            # 计算折扣比例
            if price_info['original_price'] > 0 and price_info['price'] > 0:
                discount = (price_info['original_price'] - price_info['price']) / price_info['original_price'] * 100
                price_info['discount_percent'] = round(discount, 1)
            
        except Exception as e:
            self.logger.error(f"提取价格信息失败: {e}")
        
        return price_info

    def _extract_image_url(self, container):
        """提取商品图片URL"""
        try:
            image_selectors = [
                'img.s-image',
                'img[data-image-latency]',
                '.a-dynamic-image',
                'img.product-image'
            ]
            
            for selector in image_selectors:
                img_elem = container.select_one(selector)
                if img_elem:
                    return img_elem.get('src', '') or img_elem.get('data-src', '')
            return ''
        except:
            return ''

    def _extract_product_url(self, container):
        """提取商品链接"""
        try:
            link_selectors = [
                'h2 a',
                '[data-cy="title-recipe"] a',
                '.a-link-normal'
            ]
            
            for selector in link_selectors:
                link_elem = container.select_one(selector)
                if link_elem and link_elem.get('href'):
                    href = link_elem['href']
                    if href.startswith('/'):
                        return urljoin('https://www.amazon.com', href)
                    return href
            return ''
        except:
            return ''

    def _extract_rating_info(self, container):
        """提取评分和评论数信息"""
        rating_info = {
            'rating': 0.0,
            'review_count': 0
        }
        
        try:
            # 提取评分
            rating_elem = container.select_one('.a-icon-alt')
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    try:
                        rating_info['rating'] = float(rating_match.group(1))
                    except:
                        pass
            
            # 提取评论数
            review_selectors = [
                'a[aria-label*="ratings"]',
                '.a-size-base',
                'span[aria-label*="ratings"]'
            ]
            
            for selector in review_selectors:
                review_elems = container.select(selector)
                for elem in review_elems:
                    if elem:
                        review_text = elem.get('aria-label', '') or elem.get_text(strip=True)
                        # 匹配各种评论数格式
                        review_patterns = [
                            r'(\d+)\s*ratings?',
                            r'\((\d+)\)',
                            r'(\d+)\s*reviews?',
                            r'(\d+,\d+)\s*ratings?'
                        ]
                        
                        for pattern in review_patterns:
                            review_match = re.search(pattern, review_text.replace(',', ''), re.IGNORECASE)
                            if review_match:
                                try:
                                    rating_info['review_count'] = int(review_match.group(1))
                                    break
                                except:
                                    continue
                        
                        if rating_info['review_count'] > 0:
                            break
                    
                    if rating_info['review_count'] > 0:
                        break
        
        except Exception as e:
            self.logger.error(f"提取评分信息失败: {e}")
        
        return rating_info

    def _detect_sponsored(self, container):
        """检测是否为广告商品"""
        try:
            # 检查广告标签
            sponsored_selectors = [
                '.s-sponsored-label',
                '[aria-label*="Sponsored"]',
                '.sponsored-label'
            ]
            
            for selector in sponsored_selectors:
                sponsored_elem = container.select_one(selector)
                if sponsored_elem:
                    return 1
            
            # 检查父容器的广告标识
            if container.find_parent('[data-component-type="sp-sponsored-result"]'):
                return 1
            
            # 检查文本内容
            container_text = container.get_text()
            if 'sponsored' in container_text.lower():
                return 1
            
            return 0
            
        except:
            return 0

    def _extract_bought_info(self, container):
        """提取购买数量信息"""
        try:
            container_text = container.get_text()
            
            # 匹配购买数量模式
            bought_patterns = [
                r'(\d+)\+?\s*bought\s*in\s*past\s*month',
                r'(\d+)K\+?\s*bought\s*in\s*past\s*month',
                r'(\d+)\+?\s*purchased\s*in\s*past\s*month',
                r'(\d+)K\+?\s*purchased\s*in\s*past\s*month'
            ]
            
            for pattern in bought_patterns:
                bought_match = re.search(pattern, container_text, re.IGNORECASE)
                if bought_match:
                    try:
                        bought_str = bought_match.group(1)
                        # 处理K单位
                        if 'K' in container_text.upper():
                            return int(float(bought_str) * 1000)
                        else:
                            return int(bought_str)
                    except:
                        continue
            
            return 0
            
        except:
            return 0

    def _extract_prime_info_enhanced(self, container):
        """
        增强的Prime信息提取方法 - 修复版本
        
        Args:
            container: 商品容器元素
            
        Returns:
            str: Prime配送信息
        """
        try:
            # 方法1: 通过各种Prime相关选择器
            prime_selectors = [
                'i[aria-label*="Prime"]',
                '[aria-label*="Prime"]',
                '.a-icon-prime', 
                'span[aria-label*="Prime"]',
                '.s-prime',
                '.a-color-base[aria-label*="Prime"]',
                '[class*="prime"]',
                '[data-testid*="prime"]',
                'span[data-component-type="s-prime-streaming-text"]'
            ]
            
            for selector in prime_selectors:
                prime_elem = container.select_one(selector)
                if prime_elem:
                    prime_text = prime_elem.get('aria-label', '') or prime_elem.get_text(strip=True)
                    if prime_text and 'prime' in prime_text.lower():
                        # 解析配送时间
                        delivery_info = self._parse_delivery_time(prime_text)
                        if delivery_info['delivery_time']:
                            self.logger.info(f"🚚 Prime配送时间: {delivery_info['delivery_time']} - {delivery_info['delivery_type']}")
                        
                        self.logger.info(f"✅ 找到Prime信息 (方法1): {prime_text[:50]}")
                        return prime_text[:255]
            
            # 方法2: 搜索配送相关信息
            delivery_selectors = [
                '[aria-label*="delivery"]',
                '[aria-label*="shipping"]',
                '.a-color-secondary',
                '.a-size-base-plus',
                '.a-size-small',
                '.a-size-base',
                'span[data-component-type="s-delivery-message"]'
            ]
            
            for selector in delivery_selectors:
                delivery_elems = container.select(selector)
                for elem in delivery_elems:
                    text = elem.get('aria-label', '') or elem.get_text(strip=True)
                    if text and 'prime' in text.lower():
                        delivery_info = self._parse_delivery_time(text)
                        if delivery_info['delivery_time']:
                            self.logger.info(f"🚚 Prime配送时间: {delivery_info['delivery_time']} - {delivery_info['delivery_type']}")
                        
                        self.logger.info(f"✅ 找到Prime信息 (方法2): {text[:50]}")
                        return text[:255]
            
            # 方法3: 广泛文本搜索
            container_text = container.get_text()
            
            # 更全面的Prime模式匹配
            prime_patterns = [
                r'(Prime\s+.*?delivery.*?)(?:\n|\.|\s{2,}|$)',
                r'(FREE\s+.*?Prime.*?delivery.*?)(?:\n|\.|\s{2,}|$)',
                r'(Get\s+it.*?Prime.*?)(?:\n|\.|\s{2,}|$)',
                r'(Prime\s+delivery.*?)(?:\n|\.|\s{2,}|$)',
                r'(Prime\s+FREE\s+delivery.*?)(?:\n|\.|\s{2,}|$)',
                r'(FREE\s+delivery\s+.*?Prime.*?)(?:\n|\.|\s{2,}|$)',
                r'(Prime.*?FREE\s+delivery.*?)(?:\n|\.|\s{2,}|$)',
                r'(ONE[-\s]DAY.*?Prime.*?)(?:\n|\.|\s{2,}|$)',
                r'(TWO[-\s]DAY.*?Prime.*?)(?:\n|\.|\s{2,}|$)',
                r'(SAME\s+DAY.*?Prime.*?)(?:\n|\.|\s{2,}|$)',
                r'(Prime\s+members?\s+get.*?)(?:\n|\.|\s{2,}|$)'
            ]
            
            for pattern in prime_patterns:
                match = re.search(pattern, container_text, re.IGNORECASE | re.MULTILINE)
                if match:
                    prime_info = match.group(1).strip()
                    if len(prime_info) > 5:
                        delivery_info = self._parse_delivery_time(prime_info)
                        if delivery_info['delivery_time']:
                            self.logger.info(f"🚚 Prime配送时间: {delivery_info['delivery_time']} - {delivery_info['delivery_type']}")
                        
                        self.logger.info(f"✅ 找到Prime信息 (方法3): {prime_info[:50]}")
                        return prime_info[:255]
            
            # 方法4: 简单关键词检查 - 更宽松的匹配
            if 'prime' in container_text.lower():
                lines = [line.strip() for line in container_text.split('\n') if line.strip()]
                
                # 优先匹配包含配送信息的行
                for line in lines:
                    if 'prime' in line.lower() and 5 < len(line) < 150:
                        if any(keyword in line.lower() for keyword in ['delivery', 'shipping', 'free', 'get it', 'day', 'member']):
                            delivery_info = self._parse_delivery_time(line)
                            if delivery_info['delivery_time']:
                                self.logger.info(f"🚚 Prime配送时间: {delivery_info['delivery_time']} - {delivery_info['delivery_type']}")
                            
                            self.logger.info(f"✅ 找到Prime信息 (方法4): {line[:50]}")
                            return line[:255]
                
                # 如果没找到详细信息，记录原始文本并返回简单的Prime标识
                self.logger.info(f"⚠️ 只找到Prime关键词，返回基础Prime标识")
                return 'Prime'
            
            # 如果完全没找到Prime信息，记录调试信息
            self.logger.debug(f"❌ 未找到Prime信息，容器文本: {container_text[:200]}")
            return ''

        except Exception as e:
            self.logger.error(f"提取Prime信息失败: {e}")
            return ''

    def _parse_delivery_time(self, prime_text):
        """
        解析Prime配送时间信息

        Args:
            prime_text: Prime信息文本

        Returns:
            dict: 包含配送时间信息的字典
        """
        delivery_info = {
            'delivery_time': '',
            'delivery_type': '',
            'is_free': False
        }

        if not prime_text:
            return delivery_info

        try:
            text = prime_text.lower()

            # 检查是否免费配送
            if 'free' in text:
                delivery_info['is_free'] = True

            # 解析配送时间
            import re

            # 匹配各种配送时间模式
            time_patterns = [
                r'same\s*day',
                r'one\s*day',
                r'1\s*day',
                r'two\s*day',
                r'2\s*day',
                r'next\s*day',
                r'overnight',
                r'(\d+)\s*days?',
                r'(\d+)\s*hours?',
                r'tomorrow',
                r'today'
            ]

            for pattern in time_patterns:
                match = re.search(pattern, text)
                if match:
                    if 'same' in pattern:
                        delivery_info['delivery_time'] = 'Same Day'
                        delivery_info['delivery_type'] = 'express'
                    elif 'one' in pattern or '1' in pattern:
                        delivery_info['delivery_time'] = 'One Day'
                        delivery_info['delivery_type'] = 'express'
                    elif 'two' in pattern or '2' in pattern:
                        delivery_info['delivery_time'] = 'Two Day'
                        delivery_info['delivery_type'] = 'standard'
                    elif 'next' in pattern:
                        delivery_info['delivery_time'] = 'Next Day'
                        delivery_info['delivery_type'] = 'express'
                    elif 'overnight' in pattern:
                        delivery_info['delivery_time'] = 'Overnight'
                        delivery_info['delivery_type'] = 'express'
                    elif 'tomorrow' in pattern:
                        delivery_info['delivery_time'] = 'Tomorrow'
                        delivery_info['delivery_type'] = 'express'
                    elif 'today' in pattern:
                        delivery_info['delivery_time'] = 'Today'
                        delivery_info['delivery_type'] = 'express'
                    elif match.groups():
                        days = match.group(1)
                        delivery_info['delivery_time'] = f'{days} Days'
                        delivery_info['delivery_type'] = 'standard' if int(days) > 2 else 'express'
                    break

            # 如果没有找到具体时间，但有Prime信息，设置默认值
            if not delivery_info['delivery_time'] and 'prime' in text:
                delivery_info['delivery_time'] = 'Prime Delivery'
                delivery_info['delivery_type'] = 'standard'

        except Exception as e:
            self.logger.error(f"解析配送时间失败: {e}")

        return delivery_info

    def save_products_to_db(self, products, task_id, amazon_category_id):
        """保存商品到数据库 - 修复Prime信息保存"""
        if not products:
            return {'total': 0, 'inserted': 0, 'skipped': 0, 'failed': 0}

        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            results = {'total': len(products), 'inserted': 0, 'skipped': 0, 'failed': 0}

            for product in products:
                try:
                    asin = product.get('asin', '')
                    if not asin:
                        results['failed'] += 1
                        continue

                    # 检查是否已存在
                    cursor.execute("SELECT id FROM zz_amazon_page_tasks WHERE entry_asin = %s", (asin,))
                    if cursor.fetchone():
                        results['skipped'] += 1
                        continue

                    # 获取Prime信息，确保不为空
                    prime_info = product.get('prime_info', '') or ''
                    
                    # 记录保存的Prime信息
                    if prime_info:
                        self.logger.info(f"💾 保存ASIN {asin} Prime信息: {prime_info[:50]}")
                    else:
                        self.logger.warning(f"⚠️ ASIN {asin} 没有Prime信息")

                    # 插入新记录
                    cursor.execute("""
                        INSERT INTO zz_amazon_page_tasks
                        (entry_asin, url, list_page_title, list_page_price, list_orgin_price,
                         list_page_rating, list_page_review_count, list_page_main_image_url,
                         list_page_prime_info, is_sponsored, list_bought_num, list_task_id, amazon_category_id, status)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 'pending')
                    """, (
                        asin,
                        product.get('product_url', ''),
                        product.get('title', '')[:512],
                        product.get('price', 0.00),
                        product.get('original_price', 0.00),
                        product.get('rating', 0.00),
                        product.get('review_count', 0),
                        product.get('image_url', ''),
                        prime_info,  # 确保Prime信息正确保存
                        product.get('is_sponsored', 0),
                        product.get('bought_num', 0),
                        task_id,
                        amazon_category_id
                    ))

                    results['inserted'] += 1

                except Exception as e:
                    self.logger.error(f"保存商品 {product.get('asin', 'unknown')} 失败: {e}")
                    results['failed'] += 1
                    continue

            self.logger.info(f"商品保存结果: 总计 {results['total']}, 新增 {results['inserted']}, 跳过 {results['skipped']}, 失败 {results['failed']}")
            return results

        except Exception as e:
            self.logger.error(f"保存商品数据失败: {e}")
            return {'total': len(products), 'inserted': 0, 'skipped': 0, 'failed': len(products)}
        finally:
            if 'conn' in locals():
                conn.close()

    def get_next_page_url(self, soup, current_url):
        """获取下一页URL"""
        try:
            # 查找下一页链接
            next_link = soup.select_one('a.s-pagination-next')

            if next_link and next_link.get('href'):
                next_url = next_link['href']
                if next_url.startswith('/'):
                    next_url = urljoin('https://www.amazon.com', next_url)
                return next_url

            # 备用方案：通过修改page参数
            parsed = urlparse(current_url)
            params = parse_qs(parsed.query)
            current_page = int(params.get('page', ['1'])[0])
            next_page = current_page + 1

            params['page'] = [str(next_page)]
            new_query = urlencode(params, doseq=True)
            next_url = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))

            return next_url

        except Exception as e:
            self.logger.error(f"获取下一页URL失败: {e}")
            return None

    def crawl_single_task(self, task):
        """爬取单个任务"""
        task_id = task['id']
        url = task['url']
        category_id = task['source_category_id']
        max_pages = task['max_pages_to_crawl']
        crawled_pages = task['crawled_pages']

        # 限制最大页数，避免过深抓取
        max_safe_pages = min(max_pages, 400)  # 限制最大50页
        if max_pages > max_safe_pages:
            self.logger.warning(f"任务 {task_id} 原始最大页数 {max_pages} 超过安全限制，调整为 {max_safe_pages}")
            max_pages = max_safe_pages

        self.logger.info(f"开始处理任务 {task_id}: {url} (最大页数: {max_pages})")

        try:
            # 更新任务状态为进行中
            self.update_task_status(task_id, 'in_progress')

            current_url = url
            current_page = crawled_pages + 1
            total_products = 0
            consecutive_errors = 0  # 连续错误计数
            max_consecutive_errors = 3  # 最大连续错误数

            # 如果不是从第一页开始，构建当前页URL
            if current_page > 1:
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                params['page'] = [str(current_page)]
                new_query = urlencode(params, doseq=True)
                current_url = urlunparse((
                    parsed.scheme, parsed.netloc, parsed.path,
                    parsed.params, new_query, parsed.fragment
                ))

            while current_page <= max_pages and self.running:
                self.logger.info(f"任务 {task_id} - 正在抓取第 {current_page} 页")

                try:
                    # 发送请求（传递task_id用于代理管理）
                    response = self.make_request(current_url, task_id=task_id)

                    # 解析页面
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 检查是否被反爬虫拦截
                    if "Robot Check" in response.text or "captcha" in response.text.lower():
                        self.logger.warning(f"任务 {task_id} - 检测到反爬虫验证")
                        consecutive_errors += 1
                        if consecutive_errors >= max_consecutive_errors:
                            self.logger.error(f"任务 {task_id} - 连续遇到验证码 {consecutive_errors} 次，暂停任务")
                            break
                        time.sleep(random.uniform(30, 60))
                        continue

                    # 提取商品数据
                    products = self.extract_products_from_page(soup)

                    # 检查页面是否有效（有商品数据）
                    if not products:
                        self.logger.warning(f"任务 {task_id} - 第 {current_page} 页没有找到商品，可能已到达最后一页")
                        break

                    # 保存商品数据
                    save_results = self.save_products_to_db(products, task_id, category_id)
                    total_products += save_results['inserted']

                    # 更新统计
                    with self.stats_lock:
                        self.stats['products_found'] += len(products)

                    # 重置连续错误计数
                    consecutive_errors = 0

                    # 更新已抓取页数
                    self.update_task_status(task_id, 'in_progress', crawled_pages=current_page)

                    # 更新统计
                    with self.stats_lock:
                        self.stats['pages_crawled'] += 1

                    self.logger.info(f"任务 {task_id} - 第 {current_page} 页完成，找到 {len(products)} 个商品，新增 {save_results['inserted']} 个")

                    # 检查是否有下一页
                    if current_page >= max_pages:
                        self.logger.info(f"任务 {task_id} - 已达到最大页数限制 {max_pages}")
                        break

                    next_url = self.get_next_page_url(soup, current_url)
                    if not next_url:
                        self.logger.info(f"任务 {task_id} - 没有更多页面")
                        break

                    current_url = next_url
                    current_page += 1

                    # 使用智能页面间延迟
                    delay = get_smart_delay('page', current_page, consecutive_errors)

                    self.logger.info(f"任务 {task_id} - 智能页面间延迟 {delay:.1f} 秒 (第{current_page}页)")
                    time.sleep(delay)

                except requests.exceptions.HTTPError as e:
                    if "503" in str(e):
                        self.logger.error(f"任务 {task_id} - 第 {current_page} 页遇到503错误，可能触发反爬虫限制")
                        consecutive_errors += 1

                        if consecutive_errors >= max_consecutive_errors:
                            self.logger.error(f"任务 {task_id} - 连续遇到503错误 {consecutive_errors} 次，停止任务")
                            error_msg = f"连续503错误，已抓取到第{current_page-1}页"
                            self.update_task_status(task_id, 'failed', error_message=error_msg)
                            return

                        # 503错误需要长时间等待
                        delay = random.uniform(60, 120) * consecutive_errors
                        self.logger.info(f"任务 {task_id} - 503错误延迟 {delay:.1f} 秒后继续...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"任务 {task_id} - 第 {current_page} 页HTTP错误: {e}")
                        consecutive_errors += 1
                        if consecutive_errors >= max_consecutive_errors:
                            break
                        current_page += 1
                        continue

                except Exception as e:
                    self.logger.error(f"任务 {task_id} - 抓取第 {current_page} 页失败: {e}")
                    consecutive_errors += 1

                    if consecutive_errors >= max_consecutive_errors:
                        self.logger.error(f"任务 {task_id} - 连续错误 {consecutive_errors} 次，停止任务")
                        break

                    # 普通错误的延迟
                    delay = random.uniform(10, 20)
                    time.sleep(delay)
                    current_page += 1
                    continue

            # 标记任务完成
            self.update_task_status(task_id, 'completed')

            with self.stats_lock:
                self.stats['tasks_success'] += 1

            self.logger.info(f"任务 {task_id} 完成，共抓取 {current_page-1} 页，{total_products} 个新商品")

        except Exception as e:
            error_msg = f"任务执行失败: {e}"
            self.logger.error(f"任务 {task_id} - {error_msg}")
            self.update_task_status(task_id, 'failed', error_message=error_msg)

            with self.stats_lock:
                self.stats['tasks_failed'] += 1
                self.stats['errors'].append(f"任务 {task_id}: {error_msg}")

        finally:
            with self.stats_lock:
                self.stats['tasks_processed'] += 1

    def worker_thread(self, thread_id):
        """工作线程"""
        self.logger.info(f"工作线程 {thread_id} 启动")

        while self.running:
            try:
                # 从队列获取任务
                task = self.task_queue.get(timeout=5)

                if task is None:  # 停止信号
                    break

                # 处理任务
                self.crawl_single_task(task)

                # 使用智能任务间延迟
                delay = get_smart_delay('task')
                self.logger.debug(f"工作线程 {thread_id} - 任务间延迟 {delay:.1f} 秒")
                time.sleep(delay)

                # 标记任务完成
                self.task_queue.task_done()

            except queue.Empty:
                # 队列为空，继续等待
                continue
            except Exception as e:
                self.logger.error(f"工作线程 {thread_id} 出错: {e}")
                continue

        self.logger.info(f"工作线程 {thread_id} 结束")

    def start_crawling(self):
        """启动爬虫"""
        self.logger.info("启动Amazon爬虫系统")

        # 初始化统计
        self.stats['start_time'] = datetime.now()

        # 加载待处理任务
        task_count = self.load_pending_tasks()
        if task_count == 0:
            self.logger.info("没有待处理的任务")
            return

        # 启动工作线程
        threads = []
        max_workers = min(self.config['max_workers'], task_count)

        for i in range(max_workers):
            thread = threading.Thread(
                target=self.worker_thread,
                args=(i+1,),
                name=f"Worker-{i+1}"
            )
            thread.daemon = True
            thread.start()
            threads.append(thread)

        self.logger.info(f"启动了 {max_workers} 个工作线程")

        try:
            # 等待所有任务完成
            self.task_queue.join()

        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在停止爬虫...")
            self.stop_crawling()

        # 停止所有线程
        self.running = False

        # 向队列添加停止信号
        for _ in range(max_workers):
            self.task_queue.put(None)

        # 等待线程结束
        for thread in threads:
            thread.join(timeout=10)

        # 打印统计信息
        self.print_stats()

        self.logger.info("Amazon爬虫系统已停止")

    def stop_crawling(self):
        """停止爬虫"""
        self.running = False
        self.logger.info("正在停止爬虫...")

    def print_stats(self):
        """打印统计信息 - 移除代理统计"""
        with self.stats_lock:
            stats = self.stats.copy()

        if stats['start_time']:
            duration = datetime.now() - stats['start_time']
            duration_str = str(duration).split('.')[0]  # 去掉微秒
        else:
            duration_str = "未知"

        self.logger.info("=" * 60)
        self.logger.info("爬虫统计信息")
        self.logger.info("=" * 60)
        self.logger.info(f"运行时间: {duration_str}")
        self.logger.info(f"处理任务数: {stats['tasks_processed']}")
        self.logger.info(f"成功任务数: {stats['tasks_success']}")
        self.logger.info(f"失败任务数: {stats['tasks_failed']}")
        self.logger.info(f"抓取页面数: {stats['pages_crawled']}")
        self.logger.info(f"发现商品数: {stats['products_found']}")
        self.logger.info(f"网络模式: 直连模式（代理已关闭）")

        if stats['tasks_processed'] > 0:
            success_rate = (stats['tasks_success'] / stats['tasks_processed']) * 100
            self.logger.info(f"成功率: {success_rate:.1f}%")

        if stats['errors']:
            self.logger.info(f"错误数量: {len(stats['errors'])}")
            self.logger.info("最近的错误:")
            for error in stats['errors'][-5:]:  # 显示最近5个错误
                self.logger.info(f"  - {error}")

        self.logger.info("=" * 60)

    def extract_products_from_offline_html(self, html_file_path):
        """
        支持离线HTML文件处理 - 兼容offline_test_mode
        
        Args:
            html_file_path: HTML文件路径
            
        Returns:
            list: 商品信息列表
        """
        try:
            if not os.path.exists(html_file_path):
                self.logger.error(f"文件不存在: {html_file_path}")
                return []
            
            with open(html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            soup = BeautifulSoup(html_content, 'html.parser')
            self.logger.info(f"✅ 加载离线文件: {html_file_path}")
            
            # 使用现有的提取方法
            products = self.extract_products_from_page(soup)
            
            # 生成离线测试报告
            self._generate_offline_report(products, html_file_path)
            
            return products
            
        except Exception as e:
            self.logger.error(f"处理离线文件失败: {e}")
            return []

    def _generate_offline_report(self, products, file_path):
        """生成离线测试报告 - 增强配送时间分析"""
        if not products:
            return
        
        file_name = os.path.basename(file_path)
        self.logger.info(f"📊 {file_name} 离线测试报告")
        self.logger.info("=" * 50)
        
        # Prime信息分析
        prime_count = sum(1 for p in products if p.get('prime_info'))
        prime_rate = (prime_count / len(products)) * 100 if len(products) > 0 else 0
        
        self.logger.info(f"总商品数: {len(products)}")
        self.logger.info(f"Prime信息覆盖: {prime_count}/{len(products)} ({prime_rate:.1f}%)")
        
        # 配送时间统计
        delivery_stats = {}
        for product in products:
            prime_info = product.get('prime_info', '')
            if prime_info:
                delivery_info = self._parse_delivery_time(prime_info)
                delivery_time = delivery_info.get('delivery_time', 'Unknown')
                delivery_stats[delivery_time] = delivery_stats.get(delivery_time, 0) + 1
        
        if delivery_stats:
            self.logger.info("🚚 Prime配送时间分布:")
            for delivery_time, count in sorted(delivery_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / prime_count) * 100 if prime_count > 0 else 0
                self.logger.info(f"  {delivery_time}: {count} ({percentage:.1f}%)")
        
        # 显示Prime信息示例
        prime_examples = [p for p in products[:5] if p.get('prime_info')]
        if prime_examples:
            self.logger.info("Prime配送信息示例:")
            for i, product in enumerate(prime_examples, 1):
                prime_info = product['prime_info'][:50]
                delivery_info = self._parse_delivery_time(product['prime_info'])
                delivery_time = delivery_info.get('delivery_time', 'N/A')
                self.logger.info(f"  {i}. ASIN {product['asin']}: {prime_info} [⏱️ {delivery_time}]")

    def run_offline_test_mode(self, test_files=None):
        """
        运行离线测试模式 - 兼容offline_test_mode功能
        
        Args:
            test_files: 测试文件列表，默认为标准测试文件
        """
        if test_files is None:
            test_files = [
                "doc/list.html",
                "doc/list2.html", 
                "doc/list3.html"
            ]
        
        self.logger.info("🧪 启动离线测试模式")
        self.logger.info("=" * 60)
        
        results = {'total': 0, 'success': 0, 'failed': 0, 'total_products': 0}
        
        for file_path in test_files:
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                continue
            
            results['total'] += 1
            
            try:
                products = self.extract_products_from_offline_html(file_path)
                if products:
                    results['success'] += 1
                    results['total_products'] += len(products)
                else:
                    results['failed'] += 1
            except Exception as e:
                self.logger.error(f"处理文件失败 {file_path}: {e}")
                results['failed'] += 1
        
        # 打印总结
        self.logger.info("✅ 离线测试模式完成")
        self.logger.info(f"处理文件: {results['total']}")
        self.logger.info(f"成功: {results['success']}")
        self.logger.info(f"失败: {results['failed']}")
        self.logger.info(f"总商品数: {results['total_products']}")

    def run_offline_test_mode(self, test_files=None):
        """
        运行离线测试模式 - 兼容offline_test_mode功能
        
        Args:
            test_files: 测试文件列表，默认为标准测试文件
        """
        if test_files is None:
            test_files = [
                "doc/list.html",
                "doc/list2.html", 
                "doc/list3.html"
            ]
        
        self.logger.info("🧪 启动离线测试模式")
        self.logger.info("=" * 60)
        
        results = {'total': 0, 'success': 0, 'failed': 0, 'total_products': 0}
        
        for file_path in test_files:
            if not os.path.exists(file_path):
                self.logger.warning(f"文件不存在: {file_path}")
                continue
            
            results['total'] += 1
            
            try:
                products = self.extract_products_from_offline_html(file_path)
                if products:
                    results['success'] += 1
                    results['total_products'] += len(products)
                else:
                    results['failed'] += 1
            except Exception as e:
                self.logger.error(f"处理文件失败 {file_path}: {e}")
                results['failed'] += 1
        
        # 打印总结
        self.logger.info("✅ 离线测试模式完成")
        self.logger.info(f"处理文件: {results['total']}")
        self.logger.info(f"成功: {results['success']}")
        self.logger.info(f"失败: {results['failed']}")
        self.logger.info(f"总商品数: {results['total_products']}")
        
        return results

def signal_handler(signum, frame):
    """信号处理器"""
    logger = logging.getLogger(__name__)
    logger.info(f"收到信号 {signum}，正在优雅关闭...")
    global crawler
    if crawler:
        crawler.stop_crawling()

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    logger.info("Amazon多线程爬虫系统启动")
    logger.info(f"配置信息: {CRAWLER_CONFIG}")

    try:
        # 创建爬虫实例
        global crawler
        crawler = AmazonCrawler(CRAWLER_CONFIG)

        # 启动爬虫
        crawler.start_crawling()

    except Exception as e:
        logger.error(f"爬虫系统出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

    logger.info("程序结束")

if __name__ == "__main__":
    main()
